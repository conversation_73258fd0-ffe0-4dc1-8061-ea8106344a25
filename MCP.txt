{"mcpServers": {"supabase": {"timeout": 60, "type": "stdio", "command": "cmd", "args": ["/c", "npx", "@supabase/mcp-server-supabase@latest"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}, "context7": {"timeout": 60, "type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "chrome-mcp-server": {"timeout": 60, "type": "streamableHttp", "url": "http://127.0.0.1:12306/mcp"}}}