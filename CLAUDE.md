# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个 Supabase MCP Server 项目，实现了 Model Context Protocol (MCP) 来连接 AI 助手与 Supabase 项目。该服务器允许 AI 工具（如 Cursor、Claude Desktop、VS Code 等）直接与 Supabase 项目交互。

## 包管理和开发命令

这个项目使用 `pnpm` 作为包管理器，以下是常用命令：

```bash
# 安装依赖
pnpm install

# 构建所有包
pnpm build

# 运行所有测试
pnpm test

# 运行测试覆盖率
pnpm test:coverage

# 格式化代码
pnpm format

# 检查代码格式
pnpm format:check
```

### 开发特定包的命令

```bash
# 构建并监听变化 - mcp-server-supabase
cd packages/mcp-server-supabase
pnpm dev

# 运行单元测试
pnpm test:unit

# 运行集成测试
pnpm test:integration

# 运行端到端测试
pnpm test:e2e

# 类型检查
pnpm typecheck
```

## 项目架构

### 核心包结构

- **`packages/mcp-server-supabase/`** - 主要的 MCP 服务器实现
- **`packages/mcp-utils/`** - MCP 通用工具和实用函数
- **`packages/mcp-server-postgrest/`** - PostgREST MCP 服务器（用于直接 REST API 访问）

### 主要组件

#### 1. 服务器核心 (`packages/mcp-server-supabase/src/`)

- **`server.ts`** - MCP 服务器主要实现，包含工具注册和配置
- **`index.ts`** - 入口文件和导出定义
- **`types.ts`** - 核心类型定义

#### 2. 工具系统 (`src/tools/`)

服务器按功能分组提供工具：

- **`account-tools.ts`** - 账户管理（项目、组织管理）
- **`database-operation-tools.ts`** - 数据库操作（SQL 执行、迁移）
- **`docs-tools.ts`** - 文档搜索功能
- **`debugging-tools.ts`** - 调试工具（日志、建议）
- **`development-tools.ts`** - 开发工具（类型生成、API 密钥）
- **`edge-function-tools.ts`** - Edge Functions 管理
- **`branching-tools.ts`** - 数据库分支管理
- **`storage-tools.ts`** - 存储管理

#### 3. 平台集成 (`src/platform/`)

- **Supabase Management API** 客户端实现
- **PG Meta API** 集成（数据库元数据）
- **Content API** 客户端（文档搜索）

#### 4. 传输层 (`src/transports/`)

- **`stdio.js`** - 标准输入输出传输（CLI 模式）

### 配置系统

服务器支持以下配置选项：

- **`--read-only`** - 只读模式，限制数据库写操作
- **`--project-ref`** - 项目范围限制
- **`--features`** - 启用特定功能组
- **`--api-url`** - 自定义 Supabase API 端点

### 功能组

默认启用的功能组：`docs`, `database`, `debugging`, `development`, `functions`, `branching`, `account`

## 开发注意事项

### 测试

- 使用 Vitest 作为测试框架
- 支持单元测试、集成测试和端到端测试
- 使用 MSW 进行 API 模拟
- PGLite 用于数据库测试

### 代码格式

- 使用 Biome 进行代码格式化和检查
- 配置文件：`biome.json`
- 禁用了 linter 和 organize imports

### 构建

- 使用 tsup 进行 TypeScript 构建
- 支持多种输出格式（ESM, CJS）
- 生成类型定义文件

### API 类型生成

```bash
# 生成 Management API 类型
pnpm generate:management-api-types
```

### 本地开发配置

使用 `file:` 协议在 MCP 客户端中引用本地构建：

```json
{
  "mcpServers": {
    "supabase": {
      "command": "npx",
      "args": [
        "-y",
        "@supabase/mcp-server-supabase@file:/path/to/packages/mcp-server-supabase",
      ],
      "env": {
        "SUPABASE_ACCESS_TOKEN": "<your-token>"
      }
    }
  }
}
```